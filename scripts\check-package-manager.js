#!/usr/bin/env node

/**
 * 检查包管理器脚本
 * 确保只能使用 pnpm，禁止 npm 和 yarn
 */

const chalk = require('chalk');

function checkPackageManager() {
  const userAgent = process.env.npm_config_user_agent || '';
  const execPath = process.env.npm_execpath || '';
  
  // 检查是否使用了 npm
  if (userAgent.includes('npm') && !userAgent.includes('pnpm')) {
    console.error(chalk.red.bold('❌ 错误：禁止使用 npm！'));
    console.error(chalk.yellow('请使用 pnpm 代替：'));
    console.error(chalk.cyan('  pnpm install'));
    console.error(chalk.cyan('  pnpm add <package>'));
    console.error(chalk.cyan('  pnpm remove <package>'));
    process.exit(1);
  }
  
  // 检查是否使用了 yarn
  if (userAgent.includes('yarn') || execPath.includes('yarn')) {
    console.error(chalk.red.bold('❌ 错误：禁止使用 yarn！'));
    console.error(chalk.yellow('请使用 pnpm 代替：'));
    console.error(chalk.cyan('  pnpm install'));
    console.error(chalk.cyan('  pnpm add <package>'));
    console.error(chalk.cyan('  pnpm remove <package>'));
    process.exit(1);
  }
  
  // 检查是否使用了 pnpm
  if (userAgent.includes('pnpm') || execPath.includes('pnpm')) {
    console.log(chalk.green('✅ 正在使用 pnpm，符合项目要求'));
    return;
  }
  
  // 如果都不是，可能是直接运行 node 脚本
  console.warn(chalk.yellow('⚠️  无法检测包管理器，请确保使用 pnpm'));
}

// 只在安装依赖时检查
if (process.argv.includes('install') || process.env.npm_lifecycle_event === 'preinstall') {
  checkPackageManager();
}

module.exports = checkPackageManager;
